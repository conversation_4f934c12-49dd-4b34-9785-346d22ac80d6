# AIGOV系统 Java 17 升级指南

## 升级概述

本次升级将AIGOV系统从Java 8 + Spring Boot 2.1.18升级到Java 17 + Spring Boot 2.7.18，以获得更好的性能和安全性。

## 主要变更

### 1. 版本升级
- **Java**: 1.8 → 17
- **Spring Boot**: 2.1.18.RELEASE → 2.7.18
- **Lombok**: 1.18.6 → 1.18.30
- **MapStruct**: 1.4.1.Final → 1.5.5.Final
- **Flowable**: 6.6.0 → 6.8.0
- **FastJSON**: 1.2.83 → 2.0.43
- **Druid**: 1.2.8 → 1.2.20

### 2. API文档框架变更
- **旧版本**: Swagger2 + SpringFox
- **新版本**: SpringDoc OpenAPI 3

## 需要处理的代码变更

### 1. Swagger注解迁移

#### 旧的Swagger2注解
```java
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@Api(tags = "用户管理")
@RestController
public class UserController {
    
    @ApiOperation("查询用户列表")
    @GetMapping
    public ResponseEntity<Object> query(@ApiParam("查询条件") UserQueryCriteria criteria) {
        // ...
    }
}

@ApiModel("用户")
public class User {
    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long id;
    
    @ApiModelProperty(value = "用户名", required = true)
    private String username;
}
```

#### 新的OpenAPI 3注解
```java
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;

@Tag(name = "用户管理")
@RestController
public class UserController {
    
    @Operation(summary = "查询用户列表")
    @GetMapping
    public ResponseEntity<Object> query(@Parameter(description = "查询条件") UserQueryCriteria criteria) {
        // ...
    }
}

@Schema(description = "用户")
public class User {
    @Schema(description = "用户ID", hidden = true)
    private Long id;
    
    @Schema(description = "用户名", required = true)
    private String username;
}
```

### 2. 配置文件变更

#### application.yml 需要更新的配置

```yaml
# SpringDoc OpenAPI 3 配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.hzwangda.aigov
  paths-to-match: /api/**

# 替代原来的swagger配置
# swagger:
#   enabled: true
#   title: AIGOV系统API文档
```

### 3. Spring Security配置调整

Spring Boot 2.7.x中Spring Security有一些配置变更：

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    // 旧版本配置方式（可能需要调整）
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        // 配置逻辑
    }

    // 新版本推荐配置方式
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .anyRequest().authenticated()
            )
            .build();
    }
}
```

### 4. FastJSON 2.x 变更

FastJSON升级到2.x版本，API有一些变更：

```java
// 旧版本
JSON.parseObject(jsonString, User.class);
JSON.toJSONString(user);

// 新版本（API基本兼容，但建议使用新API）
JSONB.parseObject(jsonString, User.class);
JSONB.toJSONString(user);
```

## 编译和运行

### 1. 清理和重新编译
```bash
mvn clean compile
```

### 2. 如果遇到编译错误
- 检查IDE的Java版本设置
- 确保JAVA_HOME指向Java 17
- 重新导入Maven项目

### 3. 运行应用
```bash
mvn spring-boot:run
```

## 验证升级结果

### 1. 检查应用启动
- 应用能正常启动
- 没有警告或错误日志

### 2. 检查API文档
- 访问 `http://localhost:8080/swagger-ui.html`
- 确认API文档正常显示

### 3. 功能测试
- 登录功能正常
- 主要业务功能正常
- 数据库连接正常

## 可能遇到的问题

### 1. 编译错误
- **问题**: 找不到某些类或方法
- **解决**: 检查依赖版本，可能需要调整import语句

### 2. 启动错误
- **问题**: Spring Boot自动配置冲突
- **解决**: 检查配置文件，移除过时的配置项

### 3. API文档不显示
- **问题**: Swagger UI无法访问
- **解决**: 检查SpringDoc配置，确保路径正确

### 4. 性能问题
- **问题**: 应用启动慢或运行慢
- **解决**: 调整JVM参数，优化配置

## 回滚方案

如果升级后出现严重问题，可以回滚到原版本：

1. 恢复pom.xml文件到升级前的版本
2. 使用Java 8运行环境
3. 恢复原有的配置文件

## 后续优化建议

1. **性能优化**: 利用Java 17的新特性优化代码
2. **安全加固**: 使用Spring Boot 2.7.x的安全特性
3. **监控升级**: 升级监控和日志组件
4. **测试完善**: 补充单元测试和集成测试

## 注意事项

1. 升级前请备份代码和数据库
2. 在测试环境充分验证后再部署到生产环境
3. 关注Spring Boot和相关组件的安全更新
4. 定期检查依赖的安全漏洞
