# AIGOV智治协同系统 - Augment AI 使用指南

## 项目概述

AIGOV智治协同系统是一个基于Spring Boot 2.1.18的政务办公协同平台，采用前后端分离架构，集成了工作流引擎、在线办公、文档管理等核心功能。

### 核心技术栈
- **后端**: Spring Boot 2.1.18, Spring Security, Spring Data JPA, Flowable工作流引擎
- **数据库**: MySQL/Oracle双数据库支持
- **缓存**: Redis (db7)
- **文档处理**: WPS在线编辑、金格中间件
- **存储**: 阿里云OSS对象存储

## 项目架构说明

### 模块结构
```
aigov-service/
├── bf/                          # 基础框架模块
│   ├── bf-common/              # 公共模块(工具类、配置、异常处理)
│   ├── bf-system/              # 系统核心模块(用户管理、权限、监控)
│   ├── bf-logging/             # 日志模块
│   ├── bf-tools/               # 第三方工具模块
│   └── bf-generator/           # 代码生成模块
├── aigov-workflow/             # 工作流引擎模块
├── aigov-storage-oss/          # OSS存储服务模块
├── aigov-zzkk-database/        # 中正科技数据库模块
├── aigov-officeonline/         # 在线办公模块
└── project-demo/               # 业务演示模块(主要启动入口)
```

### 业务功能域
- **用户权限管理**: 基于RBAC的权限控制体系
- **工作流管理**: 基于Flowable的流程引擎
- **公文管理**: 政务公文流转处理
- **在线办公**: WPS/金格在线编辑集成
- **文档存储**: OSS云存储服务
- **系统监控**: 服务器性能监控
- **浙政钉集成**: 政务钉钉单点登录
- **代码生成**: 基于模板的代码自动生成

## Augment AI 使用指南

### 1. 代码查询与理解

#### 查询特定功能实现
```
请帮我查找工作流引擎中流程定义的创建逻辑
```

#### 查询业务模块结构
```
请分析公文管理模块的完整业务流程
```

#### 查询技术实现细节
```
请查看OSS存储服务的文件上传实现方式
```

### 2. 代码开发与修改

#### 新增业务功能
```
请在公文管理模块中新增一个文档审批状态查询接口
```

#### 修改现有功能
```
请修改用户登录逻辑，增加登录失败次数限制功能
```

#### 集成第三方服务
```
请帮我集成钉钉机器人消息推送功能到工作流完成通知中
```

### 3. 问题排查与优化

#### 性能优化
```
请分析并优化公文列表查询的性能问题
```

#### Bug修复
```
工作流流程图显示异常，请帮我排查问题
```

#### 安全加固
```
请检查并加强文件上传的安全验证机制
```

### 4. 数据库操作

#### 数据模型分析
```
请分析工作流相关的数据表结构和关联关系
```

#### SQL优化
```
请优化公文查询的SQL语句性能
```

#### 数据迁移
```
请帮我编写从旧系统迁移用户数据的脚本
```

### 5. 代码生成与模板

#### 代码生成器使用
```
请使用代码生成器为新的业务实体生成CRUD代码
```

#### 模板定制
```
请帮我定制代码生成模板，增加审计字段
```

#### 批量生成
```
请为多个业务表批量生成标准的增删改查功能
```

## 开发规范与约定

### 代码规范
- **Java**: 遵循阿里巴巴Java开发手册
- **数据库**: 表名使用下划线命名，字段名驼峰命名
- **API**: RESTful风格，统一返回格式，**仅使用GET和POST方法**
- **HTTP方法**: 只能使用GET和POST，避免使用DELETE、PUT等方法
- **文档**: 使用Swagger进行API文档管理

### Java代码编写习惯

#### 类和接口命名
- **Entity类**: 与数据表对应，使用名词，如 `User`、`Document`、`WorkflowInstance`
- **Controller类**: 以Controller结尾，如 `UserController`、`DocumentController`
- **Service接口**: 以Service结尾，如 `UserService`、`DocumentService`
- **Service实现类**: 以ServiceImpl结尾，如 `UserServiceImpl`
- **Repository接口**: 以Repository结尾，如 `UserRepository`
- **DTO类**: 以Dto结尾，如 `UserDto`、`DocumentDto`
- **VO类**: 以Vo结尾，如 `UserVo`、`DocumentVo`
- **查询条件类**: 以QueryCriteria结尾，如 `UserQueryCriteria`

#### 方法命名规范
- **查询方法**: `findById`、`findByName`、`queryList`、`getDetail`
- **保存方法**: `save`、`create`、`update`、`saveOrUpdate`
- **删除方法**: `delete`、`deleteById`、`remove`
- **验证方法**: `validate`、`check`、`verify`
- **转换方法**: `convert`、`transform`、`toDto`、`toEntity`

#### HTTP方法使用规范
- **GET方法**: 用于查询操作，如列表查询、详情查询、导出等
- **POST方法**: 用于所有修改操作，包括新增、更新、删除等
- **禁用方法**: 不使用PUT、DELETE、PATCH等HTTP方法
- **参数传递**: 尽量避免使用路径参数(@PathVariable)，优先使用请求参数(@RequestParam)或请求体(@RequestBody)

#### 注解使用规范
```java
// Controller层 - 标准HTTP方法使用
@RestController
@RequiredArgsConstructor
@Api(tags = "用户管理")
@RequestMapping("/api/user")
@Slf4j
public class UserController {

    @ApiOperation("查询用户列表")
    @GetMapping
    @PreAuthorize("@el.check('user:list')")
    public ResponseEntity<Object> query(UserQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(userService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @ApiOperation("根据ID查询用户详情")
    @GetMapping("/detail")
    @PreAuthorize("@el.check('user:list')")
    public ResponseEntity<Object> findById(@RequestParam Long id) {
        return new ResponseEntity<>(userService.findById(id), HttpStatus.OK);
    }

    @ApiOperation("创建用户")
    @PostMapping
    @PreAuthorize("@el.check('user:add')")
    public ResponseEntity<Object> create(@Valid @RequestBody User resources) {
        return new ResponseEntity<>(userService.create(resources), HttpStatus.CREATED);
    }

    @ApiOperation("修改用户")
    @PostMapping("/update")
    @PreAuthorize("@el.check('user:edit')")
    public ResponseEntity<Object> update(@Valid @RequestBody User resources) {
        userService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @ApiOperation("删除用户")
    @PostMapping("/delete")
    @PreAuthorize("@el.check('user:del')")
    public ResponseEntity<Object> delete(@RequestBody Set<Long> ids) {
        userService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}

// Service层
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
@Slf4j
public class UserServiceImpl implements UserService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDto create(User resources) {
        // 实现逻辑
    }
}

// Entity层
@Entity
@Data
@Table(name = "sys_user")
@ApiModel("用户")
public class User extends BaseEntity {

    @Id
    @Column(name = "user_id")
    @ApiModelProperty(value = "ID", hidden = true)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "username", nullable = false, unique = true)
    @ApiModelProperty(value = "用户名")
    private String username;
}
```

#### 异常处理规范
```java
// 自定义业务异常
@Service
public class UserServiceImpl implements UserService {

    @Override
    public UserDto findById(Long id) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("用户不存在，ID: " + id));
        return userMapper.toDto(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDto create(User resources) {
        if (userRepository.findByUsername(resources.getUsername()) != null) {
            throw new EntityExistException("用户名已存在");
        }
        return userMapper.toDto(userRepository.save(resources));
    }
}

// 全局异常处理
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ApiError> handleEntityNotFound(EntityNotFoundException e) {
        return buildResponseEntity(ApiError.error(e.getMessage()));
    }
}
```

#### 日志记录规范
```java
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDto create(User resources) {
        log.info("创建用户开始，用户名: {}", resources.getUsername());

        try {
            UserDto result = userMapper.toDto(userRepository.save(resources));
            log.info("创建用户成功，用户ID: {}", result.getId());
            return result;
        } catch (Exception e) {
            log.error("创建用户失败，用户名: {}, 错误信息: {}", resources.getUsername(), e.getMessage(), e);
            throw e;
        }
    }
}
```

#### 参数验证规范
```java
// DTO验证
@Data
@ApiModel("用户创建DTO")
public class UserCreateDto {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 20, message = "用户名长度必须在2-20个字符之间")
    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @Email(message = "邮箱格式不正确")
    @ApiModelProperty(value = "邮箱")
    private String email;
}

// Controller参数验证 - 使用POST方法
@PostMapping("/create")
@ApiOperation("创建用户")
public ResponseEntity<Object> create(@Valid @RequestBody UserCreateDto userDto) {
    return new ResponseEntity<>(userService.create(userDto), HttpStatus.CREATED);
}

// 查询操作使用GET方法，避免路径参数
@GetMapping("/detail")
@ApiOperation("根据ID查询用户")
public ResponseEntity<Object> findById(@RequestParam Long id) {
    return new ResponseEntity<>(userService.findById(id), HttpStatus.OK);
}

// 删除操作使用POST方法，通过请求体传递参数
@PostMapping("/delete")
@ApiOperation("删除用户")
public ResponseEntity<Object> delete(@RequestBody Map<String, Long> params) {
    Long id = params.get("id");
    userService.deleteById(id);
    return new ResponseEntity<>(HttpStatus.OK);
}

// 或者删除多个用户
@PostMapping("/deleteByIds")
@ApiOperation("批量删除用户")
public ResponseEntity<Object> deleteByIds(@RequestBody Set<Long> ids) {
    userService.deleteAll(ids);
    return new ResponseEntity<>(HttpStatus.OK);
}
```

#### 数据库操作规范
```java
// Repository层
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户实体
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查找用户
     * @param email 邮箱
     * @return 用户实体
     */
    Optional<User> findByEmail(String email);

    /**
     * 查询启用状态的用户
     * @param enabled 启用状态
     * @return 用户列表
     */
    List<User> findByEnabled(Boolean enabled);
}

// 复杂查询使用Specification
@Service
public class UserServiceImpl implements UserService {

    @Override
    public Object queryAll(UserQueryCriteria criteria, Pageable pageable) {
        Page<User> page = userRepository.findAll((root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.isNotBlank(criteria.getUsername())) {
                predicates.add(criteriaBuilder.like(root.get("username"), "%" + criteria.getUsername() + "%"));
            }

            if (criteria.getEnabled() != null) {
                predicates.add(criteriaBuilder.equal(root.get("enabled"), criteria.getEnabled()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        }, pageable);

        return PageUtil.toPage(page.map(userMapper::toDto));
    }
}
```

#### MapStruct映射规范
```java
// Mapper接口
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserMapper extends BaseMapper<UserDto, User> {

    /**
     * 转换为DTO
     * @param entity 实体
     * @return DTO
     */
    @Override
    UserDto toDto(User entity);

    /**
     * 转换为实体
     * @param dto DTO
     * @return 实体
     */
    @Override
    User toEntity(UserDto dto);

    /**
     * 批量转换为DTO
     * @param entityList 实体列表
     * @return DTO列表
     */
    List<UserDto> toDto(List<User> entityList);

    /**
     * 创建时的映射，忽略ID和审计字段
     * @param createDto 创建DTO
     * @return 实体
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    User createDtoToEntity(UserCreateDto createDto);
}
```

#### API参数传递规范
```java
// ✅ 推荐的参数传递方式

// GET请求 - 使用@RequestParam
@GetMapping("/list")
@ApiOperation("查询用户列表")
public ResponseEntity<Object> getUserList(
    @RequestParam(required = false) String username,
    @RequestParam(required = false) Boolean enabled,
    @RequestParam(defaultValue = "0") Integer page,
    @RequestParam(defaultValue = "20") Integer size) {
    // 实现逻辑
}

@GetMapping("/detail")
@ApiOperation("查询用户详情")
public ResponseEntity<Object> getUserDetail(@RequestParam Long id) {
    return new ResponseEntity<>(userService.findById(id), HttpStatus.OK);
}

// POST请求 - 使用@RequestBody
@PostMapping("/create")
@ApiOperation("创建用户")
public ResponseEntity<Object> createUser(@RequestBody UserCreateDto userDto) {
    return new ResponseEntity<>(userService.create(userDto), HttpStatus.CREATED);
}

@PostMapping("/update")
@ApiOperation("更新用户")
public ResponseEntity<Object> updateUser(@RequestBody UserUpdateDto userDto) {
    userService.update(userDto);
    return new ResponseEntity<>(HttpStatus.OK);
}

@PostMapping("/delete")
@ApiOperation("删除用户")
public ResponseEntity<Object> deleteUser(@RequestBody Map<String, Object> params) {
    Long id = Long.valueOf(params.get("id").toString());
    userService.deleteById(id);
    return new ResponseEntity<>(HttpStatus.OK);
}

// ❌ 避免使用的方式
// @GetMapping("/detail/{id}")  // 避免路径参数
// @DeleteMapping("/delete/{id}")  // 避免DELETE方法
// @PutMapping("/update/{id}")  // 避免PUT方法
```

### 安全要求
- 所有API接口需要权限验证
- 文件上传需要类型和大小限制
- 敏感数据需要加密存储
- SQL注入防护
- 使用@PreAuthorize进行方法级权限控制

#### 工具类编写规范
```java
// 工具类示例
@UtilityClass
@Slf4j
public class FileUtils {

    /**
     * 获取文件扩展名
     * @param filename 文件名
     * @return 扩展名
     */
    public static String getExtension(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1).toLowerCase() : "";
    }

    /**
     * 验证文件类型
     * @param filename 文件名
     * @param allowedTypes 允许的类型
     * @return 是否允许
     */
    public static boolean isAllowedType(String filename, Set<String> allowedTypes) {
        String extension = getExtension(filename);
        return allowedTypes.contains(extension);
    }
}
```

#### 常量定义规范
```java
// 常量类
public final class Constants {

    private Constants() {
        throw new IllegalStateException("Utility class");
    }

    // 系统常量
    public static final String SYSTEM_USER = "system";
    public static final String DEFAULT_PASSWORD = "123456";

    // 缓存相关
    public static final String CACHE_USER = "user";
    public static final String CACHE_MENU = "menu";
    public static final int CACHE_EXPIRE_TIME = 3600;

    // 文件相关
    public static final long MAX_FILE_SIZE = 10 * 1024 * 1024L; // 10MB
    public static final Set<String> ALLOWED_IMAGE_TYPES = Set.of("jpg", "jpeg", "png", "gif");
    public static final Set<String> ALLOWED_DOCUMENT_TYPES = Set.of("doc", "docx", "pdf", "txt");
}
```

### 测试要求
- 单元测试覆盖率不低于70%
- 集成测试覆盖核心业务流程
- 性能测试关注响应时间和并发能力
- 使用@SpringBootTest进行集成测试
- 使用@MockBean进行依赖模拟

#### 单元测试示例
```java
@ExtendWith(MockitoExtension.class)
class UserServiceImplTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private UserMapper userMapper;

    @InjectMocks
    private UserServiceImpl userService;

    @Test
    @DisplayName("根据ID查找用户 - 成功")
    void findById_Success() {
        // Given
        Long userId = 1L;
        User user = new User();
        user.setId(userId);
        user.setUsername("testuser");

        UserDto userDto = new UserDto();
        userDto.setId(userId);
        userDto.setUsername("testuser");

        when(userRepository.findById(userId)).thenReturn(Optional.of(user));
        when(userMapper.toDto(user)).thenReturn(userDto);

        // When
        UserDto result = userService.findById(userId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(userId);
        assertThat(result.getUsername()).isEqualTo("testuser");

        verify(userRepository).findById(userId);
        verify(userMapper).toDto(user);
    }

    @Test
    @DisplayName("根据ID查找用户 - 用户不存在")
    void findById_UserNotFound() {
        // Given
        Long userId = 999L;
        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> userService.findById(userId))
            .isInstanceOf(EntityNotFoundException.class)
            .hasMessage("用户不存在，ID: " + userId);

        verify(userRepository).findById(userId);
        verifyNoInteractions(userMapper);
    }
}
```

## 常用开发场景

### 场景1: 新增业务模块
1. 设计数据库表结构
2. 使用代码生成器生成基础CRUD代码
3. 编写业务逻辑Service层
4. 配置权限和API接口
5. 编写单元测试和集成测试

### 场景2: 工作流集成
1. 设计流程定义BPMN文件
2. 配置流程表单和节点处理器
3. 实现业务数据与流程的绑定
4. 开发流程监控和管理功能
5. 集成待办事项和消息通知

### 场景3: 第三方系统集成
1. 分析接口文档和认证方式
2. 封装HTTP客户端调用
3. 实现数据格式转换
4. 添加异常处理和重试机制

## 注意事项

### 开发环境要求
- JDK 1.8+
- Maven 3.6+
- Redis 5.0+
- MySQL 8.0+
- Lombok插件(IDE必须安装)

### 部署相关
- 使用Docker容器化部署
- 配置文件区分环境(dev/test/prod)
- 日志文件统一管理
- 监控告警配置

### 性能考虑
- 数据库连接池配置优化
- Redis缓存策略设计
- 大文件上传分片处理
- 接口响应时间监控
